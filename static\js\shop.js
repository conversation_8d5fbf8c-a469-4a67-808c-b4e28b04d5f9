/**
 * 商城页面JavaScript
 */

let selectedItem = null;
let selectedItemPayment = null;
let itemsData = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeShop();
});

function initializeShop() {
    console.log('初始化商城页面');

    // 加载物品数据
    loadItems();

    // 初始化物品相关功能
    initItemsFeatures();
}



function loadItems() {
    // 加载物品数据
    fetch('/api/items')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                itemsData = data.items;
                console.log('物品数据加载成功:', itemsData);
                renderItems(itemsData);
            } else {
                console.error('加载物品失败:', data.error);
                showMessage('加载物品数据失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载物品失败:', error);
            showMessage('加载物品数据失败', 'error');
        });
}

function renderItems(items) {
    const itemsGrid = document.getElementById('items-grid');
    if (!itemsGrid) return;

    itemsGrid.innerHTML = '';

    items.forEach(item => {
        const itemCard = document.createElement('div');
        itemCard.className = 'item-card';
        itemCard.dataset.itemId = item.id;

        // 根据库存数量显示不同的文字描述
        let stockText = getStockText(item.stock);

        itemCard.innerHTML = `
            <div class="item-category">${item.category}</div>
            <div class="item-icon">${item.icon}</div>
            <div class="item-name">${item.name}</div>
            <div class="item-description">${item.description}</div>
            <div class="item-price">¥${item.price.toFixed(2)}</div>
            <div class="item-stock">库存: ${stockText}</div>
        `;

        // 如果库存为0，添加缺货样式
        if (item.stock === 0) {
            itemCard.classList.add('out-of-stock');
        }

        itemCard.addEventListener('click', function() {
            if (item.stock > 0) {
                selectItem(item.id);
            } else {
                showMessage('该物品暂时缺货', 'warning');
            }
        });

        itemsGrid.appendChild(itemCard);
    });
}

function getStockText(stock) {
    if (stock === 0) {
        return '无';
    } else if (stock <= 10) {
        return '少量';
    } else if (stock <= 50) {
        return '适量';
    } else {
        return '充足';
    }
}

function selectItem(itemId) {
    const item = itemsData.find(i => i.id === itemId);
    if (!item) return;

    // 清除其他选中状态
    document.querySelectorAll('.item-card').forEach(card => {
        card.classList.remove('selected');
    });

    // 设置当前选中
    const selectedCard = document.querySelector(`[data-item-id="${itemId}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }

    selectedItem = item;
    console.log('选中物品:', selectedItem);

    // 显示购买表单
    showItemPurchaseForm(item);
}

function showItemPurchaseForm(item) {
    const purchaseForm = document.getElementById('item-purchase-form');
    const selectedItemInfo = document.getElementById('selected-item-info');

    if (!purchaseForm || !selectedItemInfo) return;

    // 检查是否是特殊充值项目（ID为1或2）
    const isSpecialRecharge = item.id === 1 || item.id === 2;

    // 更新选中物品信息
    if (isSpecialRecharge) {
        // 特殊充值项目显示不同的界面
        selectedItemInfo.innerHTML = `
            <div class="selected-item-icon">${item.icon}</div>
            <div class="selected-item-details">
                <h4>${item.name}</h4>
                <p>${item.description}</p>
                <div class="selected-item-price">单价: ¥${item.price.toFixed(2)}</div>
            </div>
        `;
    } else {
        // 普通物品显示原有界面
        selectedItemInfo.innerHTML = `
            <div class="selected-item-icon">${item.icon}</div>
            <div class="selected-item-details">
                <h4>${item.name}</h4>
                <p>${item.description}</p>
                <div class="selected-item-price">¥${item.price.toFixed(2)}</div>
            </div>
        `;
    }

    // 重置表单，但保留保存的角色名
    const playerNameInput = document.getElementById('item-player-name');
    const savedPlayerName = localStorage.getItem('turtle_shop_player_name');
    if (savedPlayerName) {
        playerNameInput.value = savedPlayerName;
    } else {
        playerNameInput.value = '';
    }

    if (isSpecialRecharge) {
        // 特殊充值项目：数量表示充值金额
        document.getElementById('item-quantity').value = 1;
        // 更新数量选择器的标签和提示
        updateQuantityLabel(item);
    } else {
        // 普通物品：数量表示物品数量
        document.getElementById('item-quantity').value = 1;
        resetQuantityLabel();
    }

    updateTotalPrice();

    // 清除支付方式选择
    document.querySelectorAll('#items-tab .payment-method').forEach(method => {
        method.classList.remove('selected');
    });
    selectedItemPayment = null;

    // 显示表单
    purchaseForm.style.display = 'block';
    purchaseForm.scrollIntoView({ behavior: 'smooth' });
}







function initItemOrderForm() {
    // 初始化物品订单表单
    const itemOrderForm = document.getElementById('item-order-form');
    if (itemOrderForm) {
        itemOrderForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitItemOrder();
        });
    }
}

function initItemsFeatures() {
    // 初始化数量选择器
    initQuantitySelector();

    // 初始化物品支付方式选择
    initItemPaymentSelection();

    // 初始化取消购买按钮
    initCancelPurchase();

    // 初始化物品页面角色名验证
    initItemPlayerNameInput();

    // 初始化物品订单表单
    initItemOrderForm();
}

function initQuantitySelector() {
    const quantityInput = document.getElementById('item-quantity');
    const minusBtn = document.getElementById('quantity-minus');
    const plusBtn = document.getElementById('quantity-plus');

    if (!quantityInput || !minusBtn || !plusBtn) return;

    minusBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value) || 1;
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
            updateTotalPrice();
            updateItemSubmitButton();
        }
    });

    plusBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value) || 1;
        let maxValue;

        if (selectedItem && (selectedItem.id === 1 || selectedItem.id === 2)) {
            // 特殊充值项目：最大充值金额限制为10000元
            maxValue = 10000;
        } else {
            // 普通物品：使用max_purchase限制
            maxValue = selectedItem ? selectedItem.max_purchase : 99;
        }

        if (currentValue < maxValue) {
            quantityInput.value = currentValue + 1;
            updateTotalPrice();
            updateItemSubmitButton();
        }
    });

    quantityInput.addEventListener('input', function() {
        const value = parseInt(this.value) || 1;
        let maxValue, minValue = 1;

        if (selectedItem && (selectedItem.id === 1 || selectedItem.id === 2)) {
            // 特殊充值项目：1-10000元
            maxValue = 10000;
        } else {
            // 普通物品：使用max_purchase限制
            maxValue = selectedItem ? selectedItem.max_purchase : 99;
        }

        if (value < minValue) {
            this.value = minValue;
        } else if (value > maxValue) {
            this.value = maxValue;
        }

        updateTotalPrice();
        updateItemSubmitButton();
    });
}

function initItemPaymentSelection() {
    // 为物品页面的支付方式添加事件监听
    const itemPaymentMethods = document.querySelectorAll('#items-tab .payment-method');

    itemPaymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // 清除其他选中状态
            itemPaymentMethods.forEach(m => m.classList.remove('selected'));

            // 设置当前选中
            this.classList.add('selected');
            selectedItemPayment = this.dataset.method;

            console.log('选中物品支付方式:', selectedItemPayment);
            updateItemSubmitButton();

            // 添加选中动画效果
            this.style.transform = 'scale(1.05)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });
    });
}

function initCancelPurchase() {
    const cancelBtn = document.getElementById('cancel-purchase');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            hideItemPurchaseForm();
        });
    }
}

function updateQuantityLabel(item) {
    // 为特殊充值项目更新数量选择器的标签
    const quantityLabel = document.querySelector('label[for="item-quantity"]');
    if (quantityLabel) {
        if (item.id === 1) {
            quantityLabel.textContent = '充值数量 *';
        } else if (item.id === 2) {
            quantityLabel.textContent = '充值数量 *';
        }
    }

    // 更新数量输入框的提示
    const quantityInput = document.getElementById('item-quantity');
    if (quantityInput) {
        if (item.id === 1) {
            quantityInput.placeholder = '输入充值金额（元）';
        } else if (item.id === 2) {
            quantityInput.placeholder = '输入充值金额（元）';
        }
    }
}

function resetQuantityLabel() {
    // 重置数量选择器的标签为普通物品模式
    const quantityLabel = document.querySelector('label[for="item-quantity"]');
    if (quantityLabel) {
        quantityLabel.textContent = '购买数量 *';
    }

    const quantityInput = document.getElementById('item-quantity');
    if (quantityInput) {
        quantityInput.placeholder = '1';
    }
}

function updateTotalPrice() {
    const totalPriceElement = document.getElementById('total-price');
    const quantityInput = document.getElementById('item-quantity');

    if (!totalPriceElement || !quantityInput || !selectedItem) return;

    const quantity = parseInt(quantityInput.value) || 1;

    // 特殊处理充值项目的价格显示
    if (selectedItem.id === 1 || selectedItem.id === 2) {
        // 对于充值项目，quantity就是充值金额
        const totalPrice = quantity;
        if (selectedItem.id === 1) {
            totalPriceElement.textContent = `充值数量：${quantity}积分，总价：¥${totalPrice.toFixed(2)}`;
        } else if (selectedItem.id === 2) {
            totalPriceElement.textContent = `充值数量：${quantity}金币，总价：¥${totalPrice.toFixed(2)}`;
        }
    } else {
        // 普通物品
        const totalPrice = selectedItem.price * quantity;
        totalPriceElement.textContent = `总价：¥${totalPrice.toFixed(2)}`;
    }
}

function updateItemSubmitButton() {
    const playerName = document.getElementById('item-player-name').value.trim();
    const quantity = parseInt(document.getElementById('item-quantity').value) || 1;
    const submitBtn = document.getElementById('item-submit-btn');

    if (!submitBtn) return;

    if (selectedItem && selectedItemPayment && playerName && quantity > 0) {
        submitBtn.disabled = false;
        submitBtn.textContent = '立即购买';
        submitBtn.style.opacity = '1';
    } else {
        submitBtn.disabled = true;

        if (!selectedItem) {
            submitBtn.textContent = '请选择物品';
        } else if (!playerName) {
            submitBtn.textContent = '请输入角色名';
        } else if (!selectedItemPayment) {
            submitBtn.textContent = '请选择支付方式';
        } else {
            submitBtn.textContent = '请完善信息';
        }

        submitBtn.style.opacity = '0.6';
    }
}

function initItemPlayerNameInput() {
    const itemPlayerNameInput = document.getElementById('item-player-name');

    if (itemPlayerNameInput) {
        // 从localStorage加载保存的玩家名
        const savedPlayerName = localStorage.getItem('turtle_shop_player_name');
        if (savedPlayerName) {
            itemPlayerNameInput.value = savedPlayerName;
            // 触发验证
            setTimeout(() => {
                itemPlayerNameInput.dispatchEvent(new Event('input'));
            }, 100);
        }

        // 实时验证玩家名
        const validateInput = debounce(function() {
            const name = itemPlayerNameInput.value.trim();
            if (name) {
                const validation = validatePlayerName(name);
                if (!validation.valid) {
                    itemPlayerNameInput.style.borderColor = '#e74c3c';
                    showMessage(validation.error, 'error');
                } else {
                    itemPlayerNameInput.style.borderColor = '#27ae60';
                }
                // 实时保存到localStorage
                localStorage.setItem('turtle_shop_player_name', name);
            } else {
                itemPlayerNameInput.style.borderColor = '#ddd';
            }
            updateItemSubmitButton();
        }, 500);

        // 失去焦点时检查玩家是否存在
        const checkPlayerOnBlur = debounce(function() {
            const name = itemPlayerNameInput.value.trim();
            if (name) {
                const validation = validatePlayerName(name);
                if (validation.valid) {
                    // 显示检查状态
                    itemPlayerNameInput.style.borderColor = '#ffc107';

                    checkPlayerExists(validation.name)
                        .then(exists => {
                            if (exists) {
                                itemPlayerNameInput.style.borderColor = '#28a745';
                                showMessage('✅ 游戏角色验证成功', 'success');
                                // 保存验证成功的玩家名到localStorage
                                localStorage.setItem('turtle_shop_player_name', validation.name);
                            } else {
                                itemPlayerNameInput.style.borderColor = '#dc3545';
                                showMessage('❌ 游戏角色不存在，请检查角色名', 'error');
                            }
                        })
                        .catch(error => {
                            itemPlayerNameInput.style.borderColor = '#6c757d';
                            console.error('检查玩家失败:', error);
                        });
                }
            }
        }, 800);

        itemPlayerNameInput.addEventListener('input', validateInput);
        itemPlayerNameInput.addEventListener('blur', checkPlayerOnBlur);
    }
}

function hideItemPurchaseForm() {
    const purchaseForm = document.getElementById('item-purchase-form');
    if (purchaseForm) {
        purchaseForm.style.display = 'none';
    }

    // 清除选中状态
    document.querySelectorAll('.item-card').forEach(card => {
        card.classList.remove('selected');
    });

    selectedItem = null;
    selectedItemPayment = null;
}







function submitItemOrder() {
    const playerName = document.getElementById('item-player-name').value.trim();
    const quantity = parseInt(document.getElementById('item-quantity').value) || 1;
    const submitBtn = document.getElementById('item-submit-btn');

    // 验证表单
    if (!selectedItem || !selectedItemPayment || !playerName || quantity <= 0) {
        showMessage('请完善订单信息', 'error');
        return;
    }

    // 验证玩家名
    const validation = validatePlayerName(playerName);
    if (!validation.valid) {
        showMessage(validation.error, 'error');
        return;
    }

    // 验证购买数量
    if (quantity > selectedItem.max_purchase) {
        showMessage(`单次最多购买 ${selectedItem.max_purchase} 个`, 'error');
        return;
    }

    if (quantity > selectedItem.stock) {
        showMessage('库存不足', 'error');
        return;
    }

    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.textContent = '验证玩家中...';
    submitBtn.style.opacity = '0.6';

    // 先检查玩家是否存在
    checkPlayerExists(validation.name)
        .then(playerExists => {
            if (!playerExists) {
                throw new Error('游戏角色不存在，请检查角色名是否正确');
            }

            // 玩家存在，继续创建订单
            submitBtn.textContent = '创建订单中...';

            // 准备订单数据
            const orderData = {
                type: 'item',
                item_id: selectedItem.id,
                quantity: quantity,
                player_name: validation.name,
                payment_method: selectedItemPayment,
                total_amount: selectedItem.price * quantity
            };

            console.log('提交物品订单:', orderData);

            // 发送创建订单请求
            return sendRequest('/api/create_item_order', {
                method: 'POST',
                body: JSON.stringify(orderData)
            });
        })
        .then(data => {
            if (data.success) {
                showMessage('物品订单创建成功，正在跳转到支付页面...', 'success');

                // 跳转到支付页面
                setTimeout(() => {
                    window.location.href = '/pay/' + data.order_no;
                }, 1000);
            } else {
                throw new Error(data.error || '创建订单失败');
            }
        })
        .catch(error => {
            console.error('创建物品订单失败:', error);
            showMessage('创建订单失败: ' + error.message, 'error');

            // 恢复提交按钮
            submitBtn.disabled = false;
            submitBtn.textContent = '立即购买';
            submitBtn.style.opacity = '1';
        });
}

// 检查玩家是否存在
function checkPlayerExists(playerName) {
    return fetch(`/api/check_player?name=${encodeURIComponent(playerName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('玩家检查结果:', data);
                return data.exists;
            } else {
                console.error('检查玩家失败:', data.error);
                throw new Error(data.error || '检查玩家失败');
            }
        })
        .catch(error => {
            console.error('检查玩家API调用失败:', error);
            throw error;
        });
}

// 获取选中的商品信息
function getSelectedProductInfo() {
    if (!selectedProduct) return null;
    
    const selectedCard = document.querySelector(`[data-product-id="${selectedProduct}"]`);
    if (!selectedCard) return null;
    
    return {
        id: selectedProduct,
        name: selectedCard.querySelector('.product-name').textContent,
        amount: selectedCard.querySelector('.product-amount').textContent,
        price: selectedCard.querySelector('.product-price').textContent,
        type: selectedCard.dataset.type
    };
}



// 工具函数
function validatePlayerName(playerName) {
    if (!playerName || !playerName.trim()) {
        return { valid: false, error: '玩家名不能为空' };
    }

    const name = playerName.trim();

    if (name.length < 2) {
        return { valid: false, error: '玩家名长度不能少于2个字符' };
    }

    if (name.length > 20) {
        return { valid: false, error: '玩家名长度不能超过20个字符' };
    }

    // 检查字符格式（中文、英文、数字、下划线）
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/.test(name)) {
        return { valid: false, error: '玩家名只能包含中文、英文、数字和下划线' };
    }

    return { valid: true, name: name };
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}





function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;

    // 添加样式
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
    `;

    // 根据类型设置背景色
    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#28a745';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            messageDiv.style.backgroundColor = '#ffc107';
            messageDiv.style.color = '#212529';
            break;
        default:
            messageDiv.style.backgroundColor = '#17a2b8';
    }

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 动画显示
    setTimeout(() => {
        messageDiv.style.transform = 'translateX(0)';
        messageDiv.style.opacity = '1';
    }, 10);

    // 自动移除
    setTimeout(() => {
        messageDiv.style.transform = 'translateX(100%)';
        messageDiv.style.opacity = '0';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

function sendRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const finalOptions = { ...defaultOptions, ...options };

    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
}
