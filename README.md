# 🐢 乌龟服商城 - 拉卡拉支付版

基于拉卡拉支付的魔兽世界私服充值商城系统。

## ✨ 功能特性

- ✅ 支持支付宝、微信支付
- ✅ 扫码支付和收银台支付
- ✅ 自动充值到游戏账户
- ✅ 订单管理和状态跟踪
- ✅ 支付回调处理
- ✅ 响应式Web界面

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置证书

将拉卡拉提供的证书文件放入 `cert/` 目录：
- `api_cert.cer` - 商户证书
- `api_private_key.pem` - 商户私钥
- `lkl-apigw-v1.cer` - 拉卡拉平台证书（生产环境）
- `lkl-apigw-v2.cer` - 拉卡拉平台证书（测试环境）

### 3. 修改配置

编辑 `payment/config.py`：
```python
APPID = 'your_appid'
MERCHANT_NO = 'your_merchant_no'
TERM_NO = 'your_term_no'
NOTIFY_URL = 'http://your_domain:9000/payment/notify'
```

编辑 `shop/config.py`：
```python
GAME_SERVER_HOST = 'your_game_server_ip'
GAME_SERVER_PORT = your_game_server_port
```

### 4. 启动服务

```bash
python run.py
```

访问 http://localhost:9000 使用商城。

## 📁 项目结构

```
乌龟服商城拉卡拉/
├── app.py                 # 主应用
├── run.py                 # 启动脚本
├── requirements.txt       # 依赖包
├── cert/                  # 证书文件
├── payment/               # 支付模块
│   ├── config.py         # 支付配置
│   ├── client.py         # 拉卡拉客户端
│   └── processor.py      # 支付处理器
├── shop/                  # 商城模块
│   ├── config.py         # 商城配置
│   ├── models.py         # 数据模型
│   ├── manager.py        # 商城管理器
│   └── game_api.py       # 游戏API
├── templates/             # 页面模板
├── static/                # 静态资源
└── logs/                  # 日志文件
```

## 🔗 主要接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/` | GET | 商城首页 |
| `/api/create_order` | POST | 创建订单 |
| `/pay/<order_no>` | GET | 支付页面 |
| `/payment/notify` | POST | 支付回调 |


## 🔄 支付流程

1. 🛒 用户选择充值类型和金额
2. 📝 填写角色名称，选择支付方式
3. 💳 系统创建订单并生成支付二维码
4. 📱 用户扫码支付
5. 🔔 拉卡拉发送支付通知
6. ✅ 系统验证支付并调用游戏充值接口
7. 🎉 充值完成，更新订单状态

## ⚠️ 注意事项

- 确保服务器能接收拉卡拉支付通知
- 生产环境建议使用HTTPS
- 定期备份数据库文件
- 监控日志，及时处理异常

## 📄 许可证

仅供学习研究使用。
| API_TOKEN | API认证token | 888888 |

## 📊 订单状态

- `pending` - 待支付
- `paid` - 已支付
- `completed` - 已完成（充值成功）
- `failed` - 失败
- `cancelled` - 已取消

## 🛠️ 开发说明

### 修改充值比例

在 `shop/config.py` 中修改 `CHARGE_TYPES` 配置：

```python
'token': {
    'rate': 1.0,  # 1元=1点券
    'min_amount': 1,
    'max_amount': 10000
},
'gold': {
    'rate': 0.5,  # 1元=0.5金币
    'min_amount': 1, 
    'max_amount': 10000
}
```

### 修改端口

- **商城端口**: 在 `app.py` 和 `start.py` 中修改为9000
- **游戏服务器端口**: 在 `shop/config.py` 中修改为5000

## 📝 日志文件

- 应用日志: `logs/app.log`
- 启动日志: `logs/shop_YYYYMMDD.log`

## 🔍 故障排除

### 1. 端口冲突

```
❌ Address already in use
```

**解决方案**: 
- 检查9000端口是否被占用
- 修改app.py中的端口号

### 2. 游戏API连接失败

```
❌ 游戏API连接失败: 连接失败
```

**解决方案**: 
- 确保游戏服务器运行在localhost:5000
- 检查防火墙设置

### 3. 支付通知验证失败

```
支付通知签名验证失败
```

**解决方案**:
- 检查证书文件是否正确
- 确认回调地址配置为9000端口

## 📞 技术支持

如有问题，请查看日志文件或联系技术支持。

## 📄 许可证

本项目仅供学习和研究使用。

---

🐢 **乌龟服商城** - 让充值更简单！支持自定义金额，灵活便捷！
