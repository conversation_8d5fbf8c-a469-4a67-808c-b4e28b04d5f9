#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商城配置模块
"""

from typing import Dict, List, Any

class ShopConfig:
    """商城配置类"""
    
    def __init__(self):
        # 游戏API配置
        self.GAME_SERVER_HOST = '127.0.0.1'  # 使用127.0.0.1而不是localhost
        self.GAME_SERVER_PORT = 50000
        self.API_TOKEN = '888888'
        self.ADD_TOKEN_PATH = '/addtoken'   # 充值点券接口
        self.ADD_GOLD_PATH = '/addgold'     # 充值金币接口
        self.SEND_ITEM_PATH = '/sendmail'   # 发送物品接口
        self.CHECK_PLAYER_PATH = '/checkplayer'  # 检查玩家是否存在接口
        self.REQUEST_TIMEOUT = 10
        self.MAX_RETRIES = 3
        
        # 充值类型配置（支持自定义金额）
        self.CHARGE_TYPES = {
            'token': {
                'id': 'token',
                'name': '点券充值',
                'description': '充值游戏点券',
                'min_amount': 1,      # 最小充值数量
                'max_amount': 10000,  # 最大充值数量
                'rate': 1.0,          # 兑换比例：1元=1点券
                'unit': '点券'
            },
            'gold': {
                'id': 'gold',
                'name': '金币充值',
                'description': '充值游戏金币',
                'min_amount': 1,      # 最小充值数量
                'max_amount': 10000,  # 最大充值数量
                'rate': 50,          # 兑换比例：1元=50金币
                'unit': '金币'
            }
        }
        
        # 数据库配置
        self.DATABASE_PATH = 'shop_orders.db'
        
        # 订单配置
        self.ORDER_EXPIRE_HOURS = 24
        
        # 玩家名验证规则
        self.PLAYER_NAME_MIN_LENGTH = 2
        self.PLAYER_NAME_MAX_LENGTH = 20
        self.PLAYER_NAME_PATTERN = r'^[\u4e00-\u9fa5a-zA-Z0-9_]+$'  # 中文、英文、数字、下划线
    
    def get_all_charge_types(self) -> Dict[str, Any]:
        """获取所有充值类型"""
        return self.CHARGE_TYPES

    def get_charge_type_by_id(self, charge_type_id: str) -> Dict[str, Any]:
        """根据ID获取充值类型"""
        return self.CHARGE_TYPES.get(charge_type_id)

    def calculate_charge_amount(self, charge_type: str, money_amount: float) -> int:
        """根据充值金额计算游戏币数量"""
        charge_config = self.get_charge_type_by_id(charge_type)
        if not charge_config:
            return 0

        # 根据兑换比例计算
        return int(money_amount * charge_config['rate'])

    def validate_charge_amount(self, charge_type: str, game_amount: int) -> Dict[str, Any]:
        """验证充值数量是否合法"""
        charge_config = self.get_charge_type_by_id(charge_type)
        if not charge_config:
            return {'valid': False, 'error': '无效的充值类型'}

        if game_amount < charge_config['min_amount']:
            return {'valid': False, 'error': f'充值数量不能少于{charge_config["min_amount"]}{charge_config["unit"]}'}

        if game_amount > charge_config['max_amount']:
            return {'valid': False, 'error': f'充值数量不能超过{charge_config["max_amount"]}{charge_config["unit"]}'}

        return {'valid': True}
    
    def get_game_api_url(self, endpoint: str) -> str:
        """获取游戏API完整URL"""
        return f"http://{self.GAME_SERVER_HOST}:{self.GAME_SERVER_PORT}{endpoint}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'game_server': {
                'host': self.GAME_SERVER_HOST,
                'port': self.GAME_SERVER_PORT,
                'token': self.API_TOKEN,
                'timeout': self.REQUEST_TIMEOUT,
                'max_retries': self.MAX_RETRIES,
                'api_paths': {
                    'add_token': self.ADD_TOKEN_PATH,
                    'add_gold': self.ADD_GOLD_PATH,
                    'send_item': self.SEND_ITEM_PATH,
                    'check_player': self.CHECK_PLAYER_PATH
                }
            },
            'charge_types': self.CHARGE_TYPES,
            'database_path': self.DATABASE_PATH,
            'order_expire_hours': self.ORDER_EXPIRE_HOURS,
            'player_name_rules': {
                'min_length': self.PLAYER_NAME_MIN_LENGTH,
                'max_length': self.PLAYER_NAME_MAX_LENGTH,
                'pattern': self.PLAYER_NAME_PATTERN
            }
        }

# 创建全局配置实例
shop_config = ShopConfig()
