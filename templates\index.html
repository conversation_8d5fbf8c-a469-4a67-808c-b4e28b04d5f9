{% extends "base.html" %}

{% block content %}


<!-- 物品商城页面 -->
<div class="tab-content active" id="items-tab">
    <div class="section">
        <div class="items-grid" id="items-grid">
            <!-- 物品将通过JavaScript动态加载 -->
        </div>
    </div>

    <div class="form-section" id="item-purchase-form" style="display: none;">
        <h3>购买物品</h3>
        <div class="selected-item-info" id="selected-item-info">
            <!-- 选中物品信息 -->
        </div>

        <form id="item-order-form">
            <div class="form-group">
                <label for="item-player-name">游戏角色名 *</label>
                <div class="player-name-input-wrapper">
                    <input type="text" id="item-player-name" name="player_name" required
                           placeholder="请输入您的游戏角色名" maxlength="20"
                           list="player-name-history" autocomplete="off">
                    <datalist id="player-name-history">
                        <!-- 历史角色名将通过JavaScript动态加载 -->
                    </datalist>
                    <button type="button" class="clear-history-btn" id="clear-history-btn" title="清除历史记录">
                        🗑️
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="item-quantity">购买数量 *</label>
                <div class="quantity-input-group">
                    <button type="button" class="quantity-btn" id="quantity-minus">-</button>
                    <input type="number" id="item-quantity" name="quantity" value="1" min="1" max="99" required>
                    <button type="button" class="quantity-btn" id="quantity-plus">+</button>
                </div>
                <div class="total-price" id="total-price">
                    总价：¥0.00
                </div>
            </div>

            <div class="form-group">
                <label>支付方式 *</label>
                <div class="payment-methods">
                    <div class="payment-method" data-method="alipay">
                        <div>💰 支付宝</div>
                        <small>收银台模式</small>
                    </div>
                    <div class="payment-method" data-method="wechat">
                        <div>💚 微信支付</div>
                        <small>收银台模式</small>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancel-purchase">
                    取消购买
                </button>
                <button type="submit" class="btn" id="item-submit-btn" disabled>
                    立即购买
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/shop.js') }}"></script>
{% endblock %}
