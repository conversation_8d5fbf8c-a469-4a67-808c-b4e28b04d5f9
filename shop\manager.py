#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商城管理器
处理商品管理、订单管理等业务逻辑
"""

import sqlite3
import json
import re
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from .models import Product, Order
from .config import shop_config

logger = logging.getLogger(__name__)

class ShopManager:
    """商城管理器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or shop_config.DATABASE_PATH
        self.config = shop_config
        self.items_config = None
        self.init_database()
        self.load_items_config()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_no TEXT UNIQUE NOT NULL,
                product_id TEXT NOT NULL,
                product_name TEXT NOT NULL,
                product_type TEXT NOT NULL,
                amount INTEGER NOT NULL,
                price INTEGER NOT NULL,
                player_name TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                payment_method TEXT,
                lakala_trade_no TEXT,
                pay_order_no TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                paid_at TIMESTAMP,
                completed_at TIMESTAMP,
                failed_at TIMESTAMP,
                error_message TEXT,
                extra_data TEXT
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_no ON orders(order_no)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON orders(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_player_name ON orders(player_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON orders(created_at)')
        
        conn.commit()
        conn.close()
        
        logger.info("商城数据库初始化完成")

    def load_items_config(self):
        """加载物品配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'items_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.items_config = json.load(f)
                logger.info(f"物品配置加载成功，共 {len(self.items_config.get('items', []))} 个物品")
            else:
                logger.warning(f"物品配置文件不存在: {config_path}")
                self.items_config = {'items': [], 'categories': []}
        except Exception as e:
            logger.error(f"加载物品配置失败: {str(e)}")
            self.items_config = {'items': [], 'categories': []}

    def get_all_items(self) -> List[Dict[str, Any]]:
        """获取所有物品"""
        if not self.items_config:
            return []
        return self.items_config.get('items', [])

    def get_item_by_id(self, item_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取物品"""
        if not self.items_config:
            return None

        for item in self.items_config.get('items', []):
            if item.get('id') == item_id:
                return item
        return None

    def validate_item_purchase(self, item_id: int, quantity: int) -> Dict[str, Any]:
        """验证物品购买请求"""
        # 获取物品信息
        item = self.get_item_by_id(item_id)
        if not item:
            return {'valid': False, 'error': '物品不存在'}

        # 检查数量
        if quantity <= 0:
            return {'valid': False, 'error': '购买数量必须大于0'}

        if quantity > item.get('max_purchase', 1):
            return {'valid': False, 'error': f'单次最多购买 {item.get("max_purchase", 1)} 个'}

        if quantity > item.get('stock', 0):
            return {'valid': False, 'error': '库存不足'}

        return {
            'valid': True,
            'item': item,
            'quantity': quantity,
            'total_price': item.get('price', 0) * quantity
        }

    def validate_player_name(self, player_name: str) -> Dict[str, Any]:
        """验证玩家名"""
        if not player_name or not player_name.strip():
            return {'valid': False, 'error': '玩家名不能为空'}
        
        player_name = player_name.strip()
        
        # 检查长度
        if len(player_name) < self.config.PLAYER_NAME_MIN_LENGTH:
            return {'valid': False, 'error': f'玩家名长度不能少于{self.config.PLAYER_NAME_MIN_LENGTH}个字符'}
        
        if len(player_name) > self.config.PLAYER_NAME_MAX_LENGTH:
            return {'valid': False, 'error': f'玩家名长度不能超过{self.config.PLAYER_NAME_MAX_LENGTH}个字符'}
        
        # 检查字符格式
        if not re.match(self.config.PLAYER_NAME_PATTERN, player_name):
            return {'valid': False, 'error': '玩家名只能包含中文、英文、数字和下划线'}
        
        return {'valid': True, 'player_name': player_name}
    
    def get_charge_type_by_id(self, charge_type_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取充值类型"""
        return self.config.get_charge_type_by_id(charge_type_id)

    def get_all_charge_types(self) -> Dict[str, Any]:
        """获取所有充值类型"""
        return self.config.get_all_charge_types()

    def validate_charge_request(self, charge_type: str, money_amount: float) -> Dict[str, Any]:
        """验证充值请求"""
        # 检查充值类型
        charge_config = self.get_charge_type_by_id(charge_type)
        if not charge_config:
            return {'valid': False, 'error': '无效的充值类型'}

        # 检查金额
        if money_amount <= 0:
            return {'valid': False, 'error': '充值金额必须大于0'}

        if money_amount > 10000:  # 最大充值限制
            return {'valid': False, 'error': '单次充值金额不能超过10000元'}

        # 计算游戏币数量
        game_amount = self.config.calculate_charge_amount(charge_type, money_amount)

        # 验证游戏币数量
        amount_validation = self.config.validate_charge_amount(charge_type, game_amount)
        if not amount_validation['valid']:
            return amount_validation

        return {
            'valid': True,
            'charge_config': charge_config,
            'money_amount': money_amount,
            'game_amount': game_amount
        }
    
    def create_order(self, charge_type: str, money_amount: float, player_name: str, payment_method: str = None) -> Dict[str, Any]:
        """创建充值订单"""
        # 验证玩家名
        validation = self.validate_player_name(player_name)
        if not validation['valid']:
            return {'success': False, 'error': validation['error']}

        player_name = validation['player_name']

        # 验证充值请求
        charge_validation = self.validate_charge_request(charge_type, money_amount)
        if not charge_validation['valid']:
            return {'success': False, 'error': charge_validation['error']}

        charge_config = charge_validation['charge_config']
        game_amount = charge_validation['game_amount']
        price_cents = int(money_amount * 100)  # 转换为分

        # 创建订单
        order = Order(
            order_no=Order.generate_order_no(),
            product_id=charge_type,
            product_name=f"{charge_config['name']} - {game_amount}{charge_config['unit']}",
            product_type=charge_type,
            amount=game_amount,
            price=price_cents,
            player_name=player_name,
            payment_method=payment_method
        )

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO orders (
                    order_no, product_id, product_name, product_type,
                    amount, price, player_name, payment_method
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order.order_no, order.product_id, order.product_name, order.product_type,
                order.amount, order.price, order.player_name, order.payment_method
            ))

            conn.commit()
            conn.close()

            logger.info(f"充值订单创建成功: {order.order_no}, 类型: {charge_config['name']}, 金额: ¥{money_amount}, 数量: {game_amount}{charge_config['unit']}, 玩家: {player_name}")

            return {
                'success': True,
                'order_no': order.order_no,
                'charge_type': charge_type,
                'charge_name': charge_config['name'],
                'money_amount': money_amount,
                'game_amount': game_amount,
                'unit': charge_config['unit'],
                'player_name': player_name,
                'price': price_cents
            }

        except Exception as e:
            logger.error(f"创建充值订单失败: {str(e)}")
            return {'success': False, 'error': f'创建订单失败: {str(e)}'}

    def create_item_order(self, item_id: int, quantity: int, player_name: str, payment_method: str, total_amount: float) -> Dict[str, Any]:
        """创建物品订单"""
        # 验证玩家名
        validation = self.validate_player_name(player_name)
        if not validation['valid']:
            return {'success': False, 'error': validation['error']}

        player_name = validation['player_name']

        # 验证物品购买请求
        item_validation = self.validate_item_purchase(item_id, quantity)
        if not item_validation['valid']:
            return {'success': False, 'error': item_validation['error']}

        item = item_validation['item']
        calculated_total = item_validation['total_price']

        # 验证总金额
        if abs(total_amount - calculated_total) > 0.01:  # 允许1分钱的误差
            return {'success': False, 'error': '订单金额不匹配'}

        price_cents = int(total_amount * 100)  # 转换为分

        # 创建订单
        order = Order(
            order_no=Order.generate_order_no(),
            product_id=f"item_{item_id}",
            product_name=f"{item['name']} x{quantity}",
            product_type="item",
            amount=quantity,
            price=price_cents,
            player_name=player_name,
            payment_method=payment_method
        )

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 存储物品详细信息到extra_data
            extra_data = {
                'item_id': item_id,
                'item_name': item['name'],
                'item_description': item['description'],
                'item_icon': item['icon'],
                'item_category': item['category'],
                'unit_price': item['price'],
                'quantity': quantity,
                'total_amount': total_amount
            }

            cursor.execute('''
                INSERT INTO orders (
                    order_no, product_id, product_name, product_type,
                    amount, price, player_name, payment_method, extra_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order.order_no, order.product_id, order.product_name, order.product_type,
                order.amount, order.price, order.player_name, order.payment_method,
                json.dumps(extra_data, ensure_ascii=False)
            ))

            conn.commit()
            conn.close()

            logger.info(f"物品订单创建成功: {order.order_no}, 物品: {item['name']}, 数量: {quantity}, 金额: ¥{total_amount}, 玩家: {player_name}")

            return {
                'success': True,
                'order_no': order.order_no,
                'item_id': item_id,
                'item_name': item['name'],
                'quantity': quantity,
                'unit_price': item['price'],
                'total_amount': total_amount,
                'player_name': player_name,
                'price': price_cents
            }

        except Exception as e:
            logger.error(f"创建物品订单失败: {str(e)}")
            return {'success': False, 'error': f'创建订单失败: {str(e)}'}

    def get_order(self, order_no: str) -> Optional[Order]:
        """获取订单"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM orders WHERE order_no = ?', (order_no,))
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return Order.from_dict(dict(row))
            return None
            
        except Exception as e:
            logger.error(f"获取订单失败: {str(e)}")
            return None

    def update_order_status(self, order_no: str, status: str, **kwargs) -> Dict[str, Any]:
        """更新订单状态"""
        valid_statuses = ['pending', 'paid', 'completed', 'failed', 'cancelled']
        if status not in valid_statuses:
            return {'success': False, 'error': f'无效的订单状态: {status}'}

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建更新SQL
            update_fields = ['status = ?']
            update_values = [status]

            # 根据状态设置时间戳
            if status == 'paid':
                update_fields.append('paid_at = ?')
                update_values.append(datetime.now().isoformat())
            elif status == 'completed':
                update_fields.append('completed_at = ?')
                update_values.append(datetime.now().isoformat())
            elif status == 'failed':
                update_fields.append('failed_at = ?')
                update_values.append(datetime.now().isoformat())

            # 处理其他字段
            for key, value in kwargs.items():
                if key in ['lakala_trade_no', 'pay_order_no', 'error_message', 'extra_data']:
                    update_fields.append(f'{key} = ?')
                    update_values.append(value)

            update_values.append(order_no)

            sql = f"UPDATE orders SET {', '.join(update_fields)} WHERE order_no = ?"
            cursor.execute(sql, update_values)

            if cursor.rowcount == 0:
                conn.close()
                return {'success': False, 'error': f'订单不存在: {order_no}'}

            conn.commit()
            conn.close()

            logger.info(f"订单状态更新成功: {order_no} -> {status}")
            return {'success': True, 'order_no': order_no, 'status': status}

        except Exception as e:
            logger.error(f"更新订单状态失败: {str(e)}")
            return {'success': False, 'error': f'更新订单状态失败: {str(e)}'}

    def get_orders_by_player(self, player_name: str, limit: int = 50) -> List[Order]:
        """获取玩家的订单列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM orders
                WHERE player_name = ?
                ORDER BY created_at DESC
                LIMIT ?
            ''', (player_name, limit))

            rows = cursor.fetchall()
            conn.close()

            return [Order.from_dict(dict(row)) for row in rows]

        except Exception as e:
            logger.error(f"获取玩家订单失败: {str(e)}")
            return []

    def get_pending_orders(self, limit: int = 100) -> List[Order]:
        """获取待处理订单"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM orders
                WHERE status = 'pending'
                ORDER BY created_at ASC
                LIMIT ?
            ''', (limit,))

            rows = cursor.fetchall()
            conn.close()

            return [Order.from_dict(dict(row)) for row in rows]

        except Exception as e:
            logger.error(f"获取待处理订单失败: {str(e)}")
            return []

    def cleanup_expired_orders(self) -> int:
        """清理过期订单"""
        try:
            expire_time = datetime.now() - timedelta(hours=self.config.ORDER_EXPIRE_HOURS)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE orders
                SET status = 'cancelled'
                WHERE status = 'pending'
                AND created_at < ?
            ''', (expire_time.isoformat(),))

            cancelled_count = cursor.rowcount
            conn.commit()
            conn.close()

            if cancelled_count > 0:
                logger.info(f"清理过期订单: {cancelled_count} 个")

            return cancelled_count

        except Exception as e:
            logger.error(f"清理过期订单失败: {str(e)}")
            return 0

# 创建全局商城管理器实例
shop_manager = ShopManager()
