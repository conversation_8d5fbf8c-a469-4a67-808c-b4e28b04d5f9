/**
 * 乌龟服商城通用JavaScript函数
 */

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    
    // 添加样式
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 9999;
        max-width: 300px;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
    `;
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#27ae60';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#e74c3c';
            break;
        case 'warning':
            messageDiv.style.backgroundColor = '#f39c12';
            break;
        default:
            messageDiv.style.backgroundColor = '#3498db';
    }
    
    // 添加到页面
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// 添加动画样式
if (!document.getElementById('message-animations')) {
    const style = document.createElement('style');
    style.id = 'message-animations';
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// 格式化金额显示
function formatPrice(price) {
    return (price / 100).toFixed(2);
}

// 验证玩家名
function validatePlayerName(name) {
    if (!name || !name.trim()) {
        return { valid: false, error: '玩家名不能为空' };
    }
    
    name = name.trim();
    
    if (name.length < 2) {
        return { valid: false, error: '玩家名长度不能少于2个字符' };
    }
    
    if (name.length > 20) {
        return { valid: false, error: '玩家名长度不能超过20个字符' };
    }
    
    // 检查字符（允许中文、英文、数字、下划线）
    const pattern = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/;
    if (!pattern.test(name)) {
        return { valid: false, error: '玩家名只能包含中文、英文、数字和下划线' };
    }
    
    return { valid: true, name: name };
}

// 发送AJAX请求
function sendRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('请求失败:', error);
            throw error;
        });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('🐢 乌龟服商城页面加载完成');
    
    // 添加全局错误处理
    window.addEventListener('error', function(e) {
        console.error('页面错误:', e.error);
        showMessage('页面出现错误，请刷新重试', 'error');
    });
    
    // 添加未处理的Promise错误处理
    window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise错误:', e.reason);
        showMessage('网络请求失败，请检查网络连接', 'error');
    });
});
