#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏API集成模块
与游戏服务器通信，实现充值功能
"""

import requests
import time
import logging
from typing import Dict, Any
from .config import shop_config

logger = logging.getLogger(__name__)

class GameAPIClient:
    """游戏API客户端"""
    
    def __init__(self):
        self.config = shop_config
        self.base_url = f"http://{self.config.GAME_SERVER_HOST}:{self.config.GAME_SERVER_PORT}"
        self.token = self.config.API_TOKEN
        self.timeout = self.config.REQUEST_TIMEOUT
        self.max_retries = self.config.MAX_RETRIES
    
    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送HTTP请求到游戏服务器"""
        url = f"{self.base_url}{endpoint}"
        
        # 添加token到参数中
        params['token'] = self.token
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"发送请求到游戏服务器: {url}, 参数: {params}")
                
                response = requests.get(
                    url,
                    params=params,
                    timeout=self.timeout
                )
                
                # 检查HTTP状态码
                if response.status_code == 200:
                    result = {
                        'success': True,
                        'status_code': response.status_code,
                        'response_text': response.text,
                        'url': response.url
                    }
                    logger.info(f"游戏API请求成功: {result}")
                    return result
                else:
                    logger.warning(f"游戏API返回错误状态码: {response.status_code}, 响应: {response.text}")
                    return {
                        'success': False,
                        'status_code': response.status_code,
                        'error': f'HTTP {response.status_code}',
                        'response_text': response.text,
                        'url': response.url
                    }
                    
            except requests.exceptions.Timeout:
                logger.warning(f"游戏API请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt == self.max_retries - 1:
                    return {
                        'success': False,
                        'error': '请求超时',
                        'timeout': self.timeout
                    }
                time.sleep(1)  # 重试前等待1秒
                
            except requests.exceptions.ConnectionError:
                logger.warning(f"游戏API连接失败 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt == self.max_retries - 1:
                    return {
                        'success': False,
                        'error': '连接失败',
                        'server': f"{self.config.GAME_SERVER_HOST}:{self.config.GAME_SERVER_PORT}"
                    }
                time.sleep(1)  # 重试前等待1秒
                
            except Exception as e:
                logger.error(f"游戏API请求异常: {str(e)}")
                return {
                    'success': False,
                    'error': f'请求异常: {str(e)}'
                }
        
        return {
            'success': False,
            'error': '未知错误'
        }
    
    def add_token(self, player_name: str, amount: int) -> Dict[str, Any]:
        """
        充值积分
        调用API: http://127.0.0.1:50000/addtoken?token=888888&name=玩家名&amount=100
        """
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}
        
        if amount <= 0:
            return {'success': False, 'error': '充值数量必须大于0'}
        
        params = {
            'name': player_name.strip(),
            'amount': amount
        }
        
        result = self._make_request(self.config.ADD_TOKEN_PATH, params)
        
        # 记录充值日志
        if result['success']:
            logger.info(f"积分充值成功: 玩家={player_name}, 数量={amount}")
        else:
            logger.error(f"积分充值失败: 玩家={player_name}, 数量={amount}, 错误={result.get('error')}")
        
        return result
    
    def add_gold(self, player_name: str, amount: int) -> Dict[str, Any]:
        """
        充值金币
        调用API: http://127.0.0.1:50000/addgold?token=888888&name=玩家名&amount=50
        """
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}
        
        if amount <= 0:
            return {'success': False, 'error': '充值数量必须大于0'}
        
        params = {
            'name': player_name.strip(),
            'amount': amount
        }
        
        result = self._make_request(self.config.ADD_GOLD_PATH, params)
        
        # 记录充值日志
        if result['success']:
            logger.info(f"金币充值成功: 玩家={player_name}, 数量={amount}")
        else:
            logger.error(f"金币充值失败: 玩家={player_name}, 数量={amount}, 错误={result.get('error')}")
        
        return result

    def send_item(self, player_name: str, item_id: int, amount: int) -> Dict[str, Any]:
        """
        发送物品给玩家
        调用API: http://127.0.0.1:50000/sendmail?token=888888&name=玩家名&amount=物品数量&itemid=物品ID
        """
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}

        if amount <= 0:
            return {'success': False, 'error': '物品数量必须大于0'}

        if item_id <= 0:
            return {'success': False, 'error': '物品ID必须大于0'}

        params = {
            'name': player_name.strip(),
            'amount': amount,
            'itemid': item_id
        }

        result = self._make_request(self.config.SEND_ITEM_PATH, params)

        # 记录发送物品日志
        if result['success']:
            logger.info(f"物品发送成功: 玩家={player_name}, 物品ID={item_id}, 数量={amount}")
        else:
            logger.error(f"物品发送失败: 玩家={player_name}, 物品ID={item_id}, 数量={amount}, 错误={result.get('error')}")

        return result

    def check_player(self, player_name: str) -> Dict[str, Any]:
        """
        检查玩家是否存在
        调用API: http://127.0.0.1:50000/checkplayer?token=888888&name=游戏名
        """
        if not player_name or not player_name.strip():
            return {'success': False, 'error': '玩家名不能为空'}

        params = {
            'name': player_name.strip()
        }

        result = self._make_request(self.config.CHECK_PLAYER_PATH, params)

        # 记录查询日志
        if result['success']:
            logger.info(f"玩家查询成功: 玩家={player_name}, 存在={result.get('exists', False)}")
        else:
            logger.error(f"玩家查询失败: 玩家={player_name}, 错误={result.get('error')}")

        return result

    def test_connection(self) -> Dict[str, Any]:
        """测试与游戏服务器的连接"""
        try:
            # 尝试连接游戏服务器
            test_url = f"{self.base_url}/test"
            response = requests.get(test_url, timeout=5)
            
            return {
                'success': True,
                'message': '游戏服务器连接正常',
                'server': f"{self.config.GAME_SERVER_HOST}:{self.config.GAME_SERVER_PORT}",
                'status_code': response.status_code
            }
            
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': '无法连接到游戏服务器',
                'server': f"{self.config.GAME_SERVER_HOST}:{self.config.GAME_SERVER_PORT}"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'连接测试异常: {str(e)}'
            }
    
    def charge_by_type(self, player_name: str, charge_type: str, amount: int) -> Dict[str, Any]:
        """
        根据类型充值（统一接口）
        
        Args:
            player_name: 玩家名
            charge_type: 充值类型 (token/gold)
            amount: 充值数量
            
        Returns:
            充值结果
        """
        if charge_type == 'token':
            return self.add_token(player_name, amount)
        elif charge_type == 'gold':
            return self.add_gold(player_name, amount)
        else:
            return {
                'success': False,
                'error': f'不支持的充值类型: {charge_type}'
            }

# 创建全局游戏API客户端实例
game_api_client = GameAPIClient()
