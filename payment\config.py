#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拉卡拉支付配置模块
"""

import os
from typing import Dict, Any

class PaymentConfig:
    """支付配置类"""
    
    def __init__(self):
        # 拉卡拉商户配置
        self.APPID = 'OP10001076'
        self.MERCHANT_NO = '82230207372002Z'
        self.TERM_NO = 'M4430124'
        self.IS_TEST = False
        
        # 通知地址配置
        self.NOTIFY_URL = 'http://121.235.102.104:9000/payment/notify'
        self.RETURN_URL = 'http://121.235.102.104:9000/payment/return'
        self.CASHIER_NOTIFY_URL = 'http://121.235.102.104:9000/payment/cashier_notify'
        
        # 证书文件配置
        self.CERT_DIR = 'cert'
        self.PLATFORM_CERT_PROD = 'lkl-apigw-v1.cer'
        self.PLATFORM_CERT_TEST = 'lkl-apigw-v2.cer'
        self.MERCHANT_CERT = 'api_cert.cer'
        self.MERCHANT_KEY = 'api_private_key.pem'
        
        # 业务配置
        self.ORDER_EXPIRE_MINUTES = 20
        self.REQUEST_TIMEOUT = 30
        self.MAX_RETRIES = 3
        
        # 支持的支付方式（只保留支付宝和微信）
        self.SUPPORTED_PAY_TYPES = {
            'alipay': {
                'name': '支付宝',
                'account_type': 'ALIPAY',
                'pay_mode': 'ALIPAY',
                'use_cashier': True   # 支付宝也使用收银台模式
            },
            'wechat': {
                'name': '微信支付',
                'account_type': 'WECHAT',
                'pay_mode': 'WECHAT',
                'use_cashier': True   # 微信强制使用收银台
            }
        }
        
        # 交易类型
        self.TRANS_TYPES = {
            'qrcode': '41',      # 扫码支付
            'jsapi': '51',       # 公众号支付
            'miniprogram': '71'  # 小程序支付
        }
    
    def get_cert_path(self, cert_type: str) -> str:
        """获取证书文件路径"""
        cert_files = {
            'platform_prod': self.PLATFORM_CERT_PROD,
            'platform_test': self.PLATFORM_CERT_TEST,
            'merchant_cert': self.MERCHANT_CERT,
            'merchant_key': self.MERCHANT_KEY
        }
        
        if cert_type not in cert_files:
            raise ValueError(f"未知的证书类型: {cert_type}")
        
        # 检查是否有以APPID命名的证书文件
        if cert_type in ['merchant_cert', 'merchant_key']:
            appid_cert = f"{self.APPID}.{'cer' if cert_type == 'merchant_cert' else 'pem'}"
            appid_path = os.path.join(self.CERT_DIR, appid_cert)
            if os.path.exists(appid_path):
                return appid_path
        
        return os.path.join(self.CERT_DIR, cert_files[cert_type])
    
    def validate(self) -> Dict[str, Any]:
        """验证配置是否正确"""
        errors = []
        
        # 检查必需的配置
        required_fields = ['APPID', 'MERCHANT_NO', 'TERM_NO']
        for field in required_fields:
            if not getattr(self, field):
                errors.append(f"缺少必需配置: {field}")
        
        # 检查通知URL格式
        if not self.NOTIFY_URL.startswith(('http://', 'https://')):
            errors.append("NOTIFY_URL必须是有效的HTTP/HTTPS地址")
        
        # 检查证书文件
        cert_types = ['merchant_cert', 'merchant_key']
        if self.IS_TEST:
            cert_types.append('platform_test')
        else:
            cert_types.append('platform_prod')
        
        for cert_type in cert_types:
            cert_path = self.get_cert_path(cert_type)
            if not os.path.exists(cert_path):
                errors.append(f"证书文件不存在: {cert_path}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'appid': self.APPID,
            'merchant_no': self.MERCHANT_NO,
            'term_no': self.TERM_NO,
            'is_test': self.IS_TEST,
            'notify_url': self.NOTIFY_URL,
            'return_url': self.RETURN_URL,
            'cashier_notify_url': self.CASHIER_NOTIFY_URL,
            'order_expire_minutes': self.ORDER_EXPIRE_MINUTES,
            'supported_pay_types': self.SUPPORTED_PAY_TYPES,
            'trans_types': self.TRANS_TYPES
        }

# 创建全局配置实例
payment_config = PaymentConfig()
