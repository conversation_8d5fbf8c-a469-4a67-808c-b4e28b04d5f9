#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乌龟服商城启动脚本
"""

import os
import sys
import logging
from app import app, shop_manager

def main():
    """主启动函数"""
    print("🐢 乌龟服商城启动中...")
    
    # 检查必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('cert', exist_ok=True)
    
    # 检查证书文件
    cert_files = [
        'cert/api_cert.cer',
        'cert/api_private_key.pem', 
        'cert/lkl-apigw-v1.cer',
        'cert/lkl-apigw-v2.cer'
    ]
    
    missing_certs = []
    for cert_file in cert_files:
        if not os.path.exists(cert_file):
            missing_certs.append(cert_file)
    
    if missing_certs:
        print("⚠️ 缺少证书文件:")
        for cert in missing_certs:
            print(f"   - {cert}")
        print("请将拉卡拉证书文件放入 cert/ 目录")
        return
    
    # 清理过期订单
    try:
        expired_count = shop_manager.cleanup_expired_orders()
        if expired_count > 0:
            print(f"清理了 {expired_count} 个过期订单")
    except Exception as e:
        print(f"清理过期订单失败: {str(e)}")
    
    print(f"访问地址: http://localhost:9000")
    print(f"管理接口: http://localhost:9000/admin/orders")
    print(f"API测试: http://localhost:9000/admin/test_game_api")
    print("按 Ctrl+C 停止服务")
    
    # 启动应用
    try:
        app.run(debug=False, host='0.0.0.0', port=9000)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")

if __name__ == '__main__':
    main()
