#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乌龟服商城主应用
整合支付和商城功能的统一入口
"""

import json
import logging
import os
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for

# 导入模块
from payment import PaymentProcessor, payment_config
from shop import ShopManager, shop_config
from shop.game_api import game_api_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'turtle-server-shop-secret-key-2024'

# 创建全局实例
payment_processor = PaymentProcessor()
shop_manager = ShopManager()

class ShopApplication:
    """商城应用主类"""
    
    def __init__(self, app: Flask):
        self.app = app
        self.payment_processor = payment_processor
        self.shop_manager = shop_manager
        self.game_api = game_api_client

        # 创建日志目录
        self.logs_dir = 'logs'
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)

        # 注册路由
        self.register_routes()

    def log_order_success(self, order, payment_info=None):
        """记录成功订单到日志文件"""
        try:
            # 生成日志文件名（按日期分组）
            today = datetime.now().strftime('%Y-%m-%d')
            log_file = os.path.join(self.logs_dir, f'orders_{today}.json')

            # 准备订单数据
            order_data = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'order_no': order.order_no,
                'product_name': order.product_name,
                'product_type': order.product_type,
                'player_name': order.player_name,
                'amount': order.amount,
                'price': order.price,
                'price_yuan': f"{order.price/100:.2f}",
                'status': order.status,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else None,
                'completed_at': order.completed_at.strftime('%Y-%m-%d %H:%M:%S') if order.completed_at else None
            }

            # 如果有支付信息，添加到日志中
            if payment_info:
                order_data['payment_info'] = payment_info

            # 读取现有日志
            orders_log = []
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        orders_log = json.load(f)
                except:
                    orders_log = []

            # 添加新订单
            orders_log.append(order_data)

            # 写入日志文件
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(orders_log, f, ensure_ascii=False, indent=2)

            logger.info(f"订单日志记录成功: {order.order_no}")

        except Exception as e:
            logger.error(f"记录订单日志失败: {str(e)}")

    def register_routes(self):
        """注册所有路由"""
        # 首页
        self.app.add_url_rule('/', 'index', self.index, methods=['GET'])
        
        # 商城API
        self.app.add_url_rule('/api/products', 'get_products', self.get_products, methods=['GET'])
        self.app.add_url_rule('/api/items', 'get_items', self.get_items, methods=['GET'])
        self.app.add_url_rule('/api/create_order', 'create_order', self.create_order, methods=['POST'])
        self.app.add_url_rule('/api/create_item_order', 'create_item_order', self.create_item_order, methods=['POST'])
        self.app.add_url_rule('/api/check_payment/<order_no>', 'check_payment', self.check_payment, methods=['GET'])
        self.app.add_url_rule('/api/check_player', 'check_player_api', self.check_player_api, methods=['GET'])
        
        # 支付页面
        self.app.add_url_rule('/pay/<order_no>', 'pay_page', self.pay_page, methods=['GET'])
        
        # 支付回调
        self.app.add_url_rule('/payment/notify', 'payment_notify', self.payment_notify, methods=['POST'])
        self.app.add_url_rule('/payment/cashier_notify', 'cashier_notify', self.cashier_notify, methods=['POST'])
        self.app.add_url_rule('/payment/return', 'payment_return', self.payment_return, methods=['GET'])
        
        # 管理接口
        self.app.add_url_rule('/admin', 'admin_index', self.admin_index, methods=['GET'])
        self.app.add_url_rule('/admin/test_game_api', 'test_game_api', self.test_game_api, methods=['GET'])
        self.app.add_url_rule('/admin/test_check_player', 'test_check_player', self.test_check_player, methods=['GET'])
        self.app.add_url_rule('/admin/test_send_item', 'test_send_item', self.test_send_item, methods=['GET'])
    
    def index(self):
        """首页"""
        try:
            # 获取充值类型
            charge_types = self.shop_manager.get_all_charge_types()

            # 获取支付方式
            payment_methods = self.payment_processor.get_supported_payment_methods()

            return render_template('index.html',
                                 charge_types=charge_types,
                                 payment_methods=payment_methods)
        except Exception as e:
            logger.error(f"首页加载失败: {str(e)}")
            return f"页面加载失败: {str(e)}", 500
    
    def get_products(self):
        """获取充值类型列表API"""
        try:
            charge_types = self.shop_manager.get_all_charge_types()
            return jsonify({'success': True, 'charge_types': charge_types})
        except Exception as e:
            logger.error(f"获取充值类型失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

    def get_items(self):
        """获取物品列表API"""
        try:
            items = self.shop_manager.get_all_items()
            return jsonify({'success': True, 'items': items})
        except Exception as e:
            logger.error(f"获取物品列表失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})
    
    def create_order(self):
        """创建充值订单API"""
        try:
            data = request.get_json()
            charge_type = data.get('charge_type')
            money_amount = data.get('money_amount')
            player_name = data.get('player_name')
            payment_method = data.get('payment_method')

            # 验证参数
            if not all([charge_type, money_amount, player_name, payment_method]):
                return jsonify({'success': False, 'error': '参数不完整'})

            try:
                money_amount = float(money_amount)
            except (ValueError, TypeError):
                return jsonify({'success': False, 'error': '充值金额格式错误'})

            # 创建订单
            result = self.shop_manager.create_order(charge_type, money_amount, player_name, payment_method)

            if result['success']:
                logger.info(f"充值订单创建成功: {result['order_no']}")
                return jsonify(result)
            else:
                return jsonify(result)

        except Exception as e:
            logger.error(f"创建充值订单异常: {str(e)}")
            return jsonify({'success': False, 'error': '服务器错误'})

    def create_item_order(self):
        """创建物品订单API"""
        try:
            data = request.get_json()
            if not data:
                logger.error("创建物品订单失败: 请求数据为空")
                return jsonify({'success': False, 'error': '请求数据格式错误'})

            item_id = data.get('item_id')
            quantity = data.get('quantity')
            player_name = data.get('player_name')
            payment_method = data.get('payment_method')
            total_amount = data.get('total_amount')

            logger.info(f"收到物品订单请求: {data}")

            # 验证参数
            if not all([item_id, quantity, player_name, payment_method, total_amount]):
                return jsonify({'success': False, 'error': '参数不完整'})

            try:
                item_id = int(item_id)
                quantity = int(quantity)
                total_amount = float(total_amount)
            except (ValueError, TypeError):
                return jsonify({'success': False, 'error': '参数格式错误'})

            # 创建物品订单
            result = self.shop_manager.create_item_order(item_id, quantity, player_name, payment_method, total_amount)

            if result['success']:
                logger.info(f"物品订单创建成功: {result['order_no']}")
                return jsonify(result)
            else:
                return jsonify(result)

        except Exception as e:
            logger.error(f"创建物品订单异常: {str(e)}")
            return jsonify({'success': False, 'error': '服务器错误'})
    
    def pay_page(self, order_no: str):
        """支付页面"""
        try:
            # 获取订单信息
            order = self.shop_manager.get_order(order_no)
            if not order:
                return "订单不存在", 404

            # 检查订单状态
            if order.status == 'completed':
                return redirect(url_for('payment_return') + f'?out_trade_no={order_no}&status=success')
            elif order.status == 'failed':
                return redirect(url_for('payment_return') + f'?out_trade_no={order_no}&status=failed')
            elif order.status != 'pending':
                return f"订单状态异常: {order.get_display_status()}", 400

            # 检查是否已经有拉卡拉交易号（避免重复创建）
            if order.lakala_trade_no and order.extra_data:
                try:
                    extra_data = json.loads(order.extra_data)
                    if 'qr_code' in extra_data:
                        # 已有二维码，直接显示
                        logger.info(f"使用已存在的支付订单: {order.lakala_trade_no}")
                        return render_template('payment.html',
                                             order=order.to_dict(),
                                             qr_code=extra_data['qr_code'],
                                             payment_method=order.payment_method,
                                             payment_type=extra_data.get('payment_type', 'qr'),
                                             qr_url=extra_data.get('qr_url'),
                                             fallback=extra_data.get('fallback', False))
                except Exception as e:
                    logger.warning(f"解析extra_data失败: {e}")
                    pass

            # 创建新的支付订单
            payment_result = self.payment_processor.create_payment(
                order_no=order.order_no,
                payment_method=order.payment_method,
                amount=order.price,
                subject=f"{order.product_name} - {order.player_name}",
                request_ip=request.remote_addr or '127.0.0.1'
            )

            if not payment_result['success']:
                return f"创建支付订单失败: {payment_result['error']}", 500

            # 更新订单信息
            update_data = {}
            if payment_result.get('lakala_trade_no'):
                update_data['lakala_trade_no'] = payment_result['lakala_trade_no']
            if payment_result.get('pay_order_no'):
                update_data['pay_order_no'] = payment_result['pay_order_no']

            # 保存二维码到extra_data中，保留原有数据
            if payment_result.get('qr_code'):
                # 获取现有的extra_data
                existing_extra_data = {}
                if order.extra_data:
                    try:
                        existing_extra_data = json.loads(order.extra_data)
                    except json.JSONDecodeError:
                        logger.warning(f"订单额外数据解析失败: {order_no}")

                # 添加支付相关信息到现有数据中
                existing_extra_data.update({
                    'qr_code': payment_result['qr_code'],
                    'payment_code': payment_result.get('payment_code'),
                    'created_time': datetime.now().isoformat()
                })

                update_data['extra_data'] = json.dumps(existing_extra_data, ensure_ascii=False)
                logger.info(f"准备保存extra_data，长度: {len(update_data['extra_data'])}")

            if update_data:
                logger.info(f"更新订单数据: {list(update_data.keys())}")
                result = self.shop_manager.update_order_status(order_no, 'pending', **update_data)
                logger.info(f"订单更新结果: {result}")

                # 验证更新是否成功
                updated_order = self.shop_manager.get_order(order_no)
                if updated_order and updated_order.extra_data:
                    logger.info(f"验证：extra_data已保存，长度: {len(updated_order.extra_data)}")
                else:
                    logger.warning(f"验证：extra_data未保存或为空")

            # 统一显示支付页面（所有支付方式都使用收银台二维码模式）
            return render_template('payment.html',
                                 order=order.to_dict(),
                                 qr_code=payment_result.get('qr_code'),
                                 payment_method=order.payment_method,
                                 payment_type=payment_result.get('payment_type', 'qr'),
                                 qr_url=payment_result.get('qr_url'),
                                 fallback=payment_result.get('fallback', False))
                                     
        except Exception as e:
            logger.error(f"支付页面异常: {str(e)}")
            return f"支付页面加载失败: {str(e)}", 500
    
    def check_payment(self, order_no: str):
        """检查支付状态API"""
        try:
            order = self.shop_manager.get_order(order_no)
            if not order:
                return jsonify({'success': False, 'error': '订单不存在'})
            
            return jsonify({
                'success': True,
                'status': order.status,
                'display_status': order.get_display_status(),
                'error': order.error_message
            })
            
        except Exception as e:
            logger.error(f"检查支付状态异常: {str(e)}")
            return jsonify({'success': False, 'error': '服务器错误'})

    def check_player_api(self):
        """检查玩家是否存在API"""
        try:
            player_name = request.args.get('name')
            if not player_name or not player_name.strip():
                return jsonify({'success': False, 'error': '玩家名不能为空'})

            # 调用游戏API检查玩家
            result = self.game_api.check_player(player_name.strip())

            if result['success']:
                # 解析游戏服务器的响应，判断玩家是否存在
                response_text = result.get('response_text', '')
                exists = '玩家存在:' in response_text

                logger.info(f"玩家检查: {player_name}, 存在: {exists}")

                return jsonify({
                    'success': True,
                    'exists': exists,
                    'player_name': player_name,
                    'message': response_text
                })
            else:
                logger.error(f"检查玩家失败: {player_name}, 错误: {result.get('error')}")
                return jsonify({
                    'success': False,
                    'error': result.get('error', '检查玩家失败')
                })

        except Exception as e:
            logger.error(f"检查玩家API异常: {str(e)}")
            return jsonify({'success': False, 'error': '服务器错误'})

    def payment_notify(self):
        """拉卡拉支付异步通知处理"""
        try:
            # 获取请求体和头部信息
            body = request.get_data(as_text=True)
            authorization = request.headers.get('Authorization')
            content_type = request.headers.get('Content-Type')

            # 详细记录通知信息
            logger.info(f"🔔 收到支付通知:")
            logger.info(f"  Content-Type: {content_type}")
            logger.info(f"  Authorization: {authorization}")
            logger.info(f"  Body: {body}")
            logger.info(f"  Headers: {dict(request.headers)}")

            # 临时跳过签名验证，先让通知能够被处理
            # TODO: 生产环境中需要启用签名验证
            skip_signature_verification = True

            if not skip_signature_verification:
                if not authorization:
                    logger.warning("支付通知缺少签名")
                    return 'no sign', 400

                # 验证签名
                if not self.payment_processor.verify_notify(authorization, body):
                    logger.warning("支付通知签名验证失败")
                    return 'fail', 400
            else:
                logger.info("⚠️ 临时跳过签名验证")

            # 解析通知数据
            data = self.payment_processor.parse_notify_data(body)
            if not data:
                logger.warning("支付通知数据格式错误")
                return 'no data', 400

            logger.info(f"解析的通知数据: {data}")

            # 处理通知逻辑 - 使用拉卡拉的字段名
            out_trade_no = data.get('out_trade_no')  # 拉卡拉使用out_trade_no
            api_trade_no = data.get('trade_no')
            total_amount = data.get('total_amount')
            buyer = data.get('user_id2', '')
            trade_status = data.get('trade_status')  # 拉卡拉使用trade_status
            trade_state = data.get('trade_state')    # 也可能使用trade_state

            logger.info(f"🔔 收到支付通知: 订单={out_trade_no}, 状态={trade_status}/{trade_state}, 金额={total_amount}")
            logger.info(f"   拉卡拉交易号: {api_trade_no}")
            logger.info(f"   买家信息: {buyer}")

            # 拉卡拉支付成功状态是'SUCCESS'
            if trade_status == 'SUCCESS' or trade_state == 'SUCCESS':
                logger.info(f"✅ 支付成功，开始处理充值...")
                # 处理支付成功
                result = self.process_payment_success(out_trade_no, api_trade_no, total_amount, buyer)
                if result['success']:
                    logger.info(f"🎉 支付处理成功: {out_trade_no}")
                else:
                    logger.error(f"❌ 支付处理失败: {out_trade_no}, 错误: {result['error']}")
            else:
                logger.info(f"⏳ 支付状态: {trade_status}/{trade_state} (非成功状态)")

            return 'success'

        except Exception as e:
            logger.error(f"支付通知处理异常: {str(e)}")
            return 'fail', 500

    def cashier_notify(self):
        """收银台支付异步通知处理"""
        try:
            # 获取请求体
            body = request.get_data(as_text=True)
            authorization = request.headers.get('Authorization')

            if not authorization:
                logger.warning("收银台通知缺少签名")
                return 'no sign', 400

            # 验证签名 (收银台通知暂时跳过签名验证)
            logger.info("⚠️ 收银台通知暂时跳过签名验证")
            # if not self.payment_processor.verify_notify(authorization, body):
            #     logger.warning("收银台通知签名验证失败")
            #     return 'fail', 400

            # 解析通知数据
            data = self.payment_processor.parse_notify_data(body)
            if not data:
                logger.warning("收银台通知数据格式错误")
                return 'no data', 400

            # 处理收银台通知逻辑
            out_order_no = data.get('out_order_no')
            order_trade_info = data.get('order_trade_info', {})
            api_trade_no = order_trade_info.get('trade_no')
            total_amount = data.get('total_amount')
            buyer = order_trade_info.get('user_id2')
            order_status = data.get('order_status')

            logger.info(f"收到收银台通知: 订单={out_order_no}, 状态={order_status}, 金额={total_amount}")

            if order_status == '2':  # 支付成功
                # 处理支付成功
                result = self.process_payment_success(out_order_no, api_trade_no, total_amount, buyer)
                if result['success']:
                    logger.info(f"收银台支付处理成功: {out_order_no}")
                else:
                    logger.error(f"收银台支付处理失败: {out_order_no}, 错误: {result['error']}")

            return 'success'

        except Exception as e:
            logger.error(f"收银台通知处理异常: {str(e)}")
            return 'fail', 500

    def process_payment_success(self, order_no: str, api_trade_no: str, total_amount: str, buyer: str) -> dict:
        """处理支付成功逻辑"""
        try:
            # 获取订单信息
            order = self.shop_manager.get_order(order_no)
            if not order:
                return {'success': False, 'error': f'订单不存在: {order_no}'}

            # 检查订单状态
            if order.status == 'completed':
                logger.info(f"订单已处理: {order_no}")
                return {'success': True, 'message': '订单已处理'}

            if order.status != 'pending':
                return {'success': False, 'error': f'订单状态异常: {order.status}'}

            # 验证金额
            if int(total_amount) != order.price:
                return {'success': False, 'error': f'支付金额不匹配: 期望{order.price}, 实际{total_amount}'}

            # 更新订单为已支付，保留原有的extra_data并添加buyer信息
            existing_extra_data = {}
            if order.extra_data:
                try:
                    existing_extra_data = json.loads(order.extra_data)
                except json.JSONDecodeError:
                    logger.warning(f"订单额外数据解析失败: {order_no}")

            # 添加buyer信息到现有数据中
            existing_extra_data['buyer'] = buyer

            self.shop_manager.update_order_status(
                order_no, 'paid',
                lakala_trade_no=api_trade_no,
                extra_data=json.dumps(existing_extra_data, ensure_ascii=False)
            )

            # 根据订单类型选择不同的处理逻辑
            if order.product_type == 'item':
                # 物品订单处理
                result = self.process_item_order(order)
            else:
                # 充值订单处理
                result = self.process_charge_order(order)

            if result['success']:
                # 处理成功，更新订单状态
                self.shop_manager.update_order_status(order_no, 'completed')
                logger.info(f"订单处理成功: 订单={order_no}, 玩家={order.player_name}, 类型={order.product_type}")

                # 记录成功订单到日志文件
                updated_order = self.shop_manager.get_order(order_no)  # 获取更新后的订单信息
                payment_info = {
                    'api_trade_no': api_trade_no,
                    'total_amount': total_amount,
                    'buyer': buyer
                }
                self.log_order_success(updated_order, payment_info)

                return {'success': True, 'message': result['message']}
            else:
                # 处理失败，更新订单状态
                self.shop_manager.update_order_status(
                    order_no, 'failed',
                    error_message=result.get('error', '未知错误')
                )
                logger.error(f"订单处理失败: 订单={order_no}, 错误={result.get('error')}")
                return {'success': False, 'error': result.get('error', '未知错误')}

        except Exception as e:
            logger.error(f"处理支付成功异常: {str(e)}")
            return {'success': False, 'error': f'处理异常: {str(e)}'}

    def process_item_order(self, order) -> dict:
        """处理物品订单"""
        try:
            # 解析物品订单的额外数据
            extra_data = {}
            if order.extra_data:
                try:
                    extra_data = json.loads(order.extra_data)
                except json.JSONDecodeError:
                    logger.warning(f"订单额外数据解析失败: {order.order_no}")

            # 获取物品ID和数量
            item_id = extra_data.get('item_id')
            quantity = extra_data.get('quantity', order.amount)
            item_name = extra_data.get('item_name', '未知物品')

            # 如果extra_data中没有item_id，尝试从product_id中提取
            if not item_id and order.product_id.startswith('item_'):
                try:
                    item_id = int(order.product_id.replace('item_', ''))
                    logger.info(f"从product_id中提取物品ID: {item_id}")
                except ValueError:
                    logger.error(f"无法从product_id中提取物品ID: {order.product_id}")

            if not item_id:
                logger.error(f"物品ID缺失: extra_data={extra_data}, product_id={order.product_id}")
                return {'success': False, 'error': '物品ID缺失'}

            # 特殊处理：ID为1的点券充值和ID为2的金币充值
            if item_id == 1:
                # 点券充值：1元 = 1点券，quantity表示充值金额（元）
                points_amount = quantity
                charge_result = self.game_api.add_token(order.player_name, points_amount)

                if charge_result['success']:
                    logger.info(f"点券充值成功: 订单={order.order_no}, 玩家={order.player_name}, 点券={points_amount}")
                    return {'success': True, 'message': f'点券充值成功: {points_amount}点券'}
                else:
                    logger.error(f"点券充值失败: 订单={order.order_no}, 错误={charge_result.get('error')}")
                    return {'success': False, 'error': f"点券充值失败: {charge_result.get('error', '未知错误')}"}

            elif item_id == 2:
                # 金币充值：quantity表示充值金额（元），需要转换为金币数量
                # 这里假设1元=1金币，可以根据实际需求调整比例
                gold_amount = quantity
                charge_result = self.game_api.add_gold(order.player_name, gold_amount)

                if charge_result['success']:
                    logger.info(f"金币充值成功: 订单={order.order_no}, 玩家={order.player_name}, 金币={gold_amount}")
                    return {'success': True, 'message': f'金币充值成功: {gold_amount}金币'}
                else:
                    logger.error(f"金币充值失败: 订单={order.order_no}, 错误={charge_result.get('error')}")
                    return {'success': False, 'error': f"金币充值失败: {charge_result.get('error', '未知错误')}"}
            else:
                # 普通物品发送
                send_result = self.game_api.send_item(order.player_name, item_id, quantity)

                if send_result['success']:
                    logger.info(f"物品发送成功: 订单={order.order_no}, 玩家={order.player_name}, 物品={item_name}(ID:{item_id}), 数量={quantity}")
                    return {'success': True, 'message': f'物品发送成功: {item_name} x{quantity}'}
                else:
                    logger.error(f"物品发送失败: 订单={order.order_no}, 错误={send_result.get('error')}")
                    return {'success': False, 'error': f"物品发送失败: {send_result.get('error', '未知错误')}"}

        except Exception as e:
            logger.error(f"处理物品订单异常: {str(e)}")
            return {'success': False, 'error': f'处理物品订单异常: {str(e)}'}

    def process_charge_order(self, order) -> dict:
        """处理充值订单"""
        try:
            # 调用游戏充值接口
            charge_result = self.game_api.charge_by_type(order.player_name, order.product_type, order.amount)

            if charge_result['success']:
                logger.info(f"充值成功: 订单={order.order_no}, 玩家={order.player_name}, 类型={order.product_type}, 数量={order.amount}")
                return {'success': True, 'message': '充值成功'}
            else:
                logger.error(f"充值失败: 订单={order.order_no}, 错误={charge_result.get('error')}")
                return {'success': False, 'error': f"充值失败: {charge_result.get('error', '未知错误')}"}

        except Exception as e:
            logger.error(f"处理充值订单异常: {str(e)}")
            return {'success': False, 'error': f'处理充值订单异常: {str(e)}'}

    def payment_return(self):
        """同步返回页面"""
        # 记录所有URL参数
        logger.info(f"🔙 收到支付返回请求，URL参数: {dict(request.args)}")

        order_no = request.args.get('out_order_no', request.args.get('out_trade_no', ''))
        url_status = request.args.get('status', '')  # 从URL获取状态

        logger.info(f"🔙 解析参数: 订单号={order_no}, URL状态={url_status}")

        if order_no:
            # 检查订单状态
            order = self.shop_manager.get_order(order_no)
            if order:
                # 优先使用URL传递的状态，然后使用数据库状态
                if url_status == 'success' or order.status == 'completed':
                    message = f"🎉 支付成功！\n\n充值详情：\n• 订单号：{order_no}\n• 商品：{order.product_name}\n• 角色：{order.player_name}\n• 状态：充值已完成\n\n<span class='important-notice'>⚠️ 重要提示：领取方式</span>\n进入游戏，找到NPC综合商城，点击领取充值的点券和金币选项！"
                    status = "success"
                elif url_status == 'failed' or order.status == 'failed':
                    error_msg = order.error_message or '未知错误'
                    message = f"❌ 充值失败\n\n订单号：{order_no}\n错误原因：{error_msg}\n\n如有疑问请联系客服。"
                    status = "error"
                else:
                    message = f"⏳ 支付处理中\n\n订单号：{order_no}\n请稍候，系统正在处理您的支付..."
                    status = "processing"
            else:
                message = f"❌ 订单不存在\n\n订单号：{order_no}\n请检查订单号是否正确。"
                status = "error"
        else:
            message = "❌ 缺少订单信息\n\n请从正确的支付页面访问。"
            status = "error"

        return render_template('result.html',
                             message=message,
                             status=status,
                             order_no=order_no,
                             order=order.to_dict() if order else None)

    def admin_index(self):
        """管理后台首页"""
        try:
            return render_template('admin.html')
        except Exception as e:
            logger.error(f"管理后台首页加载失败: {str(e)}")
            return f"管理后台加载失败: {str(e)}", 500



    def test_game_api(self):
        """测试游戏API连接"""
        try:
            result = self.game_api.test_connection()
            return jsonify(result)
        except Exception as e:
            logger.error(f"测试游戏API失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

    def test_check_player(self):
        """测试检查玩家API"""
        try:
            player_name = request.args.get('name', '测试玩家')
            result = self.game_api.check_player(player_name)
            return jsonify({
                'api': 'check_player',
                'player_name': player_name,
                'result': result
            })
        except Exception as e:
            logger.error(f"测试检查玩家API失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

    def test_send_item(self):
        """测试发送物品API"""
        try:
            player_name = request.args.get('name', '测试玩家')
            item_id = int(request.args.get('itemid', 1001))
            amount = int(request.args.get('amount', 1))

            result = self.game_api.send_item(player_name, item_id, amount)
            return jsonify({
                'api': 'send_item',
                'player_name': player_name,
                'item_id': item_id,
                'amount': amount,
                'result': result
            })
        except Exception as e:
            logger.error(f"测试发送物品API失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})

# 创建应用实例
shop_app = ShopApplication(app)

if __name__ == '__main__':
    print("🐢 乌龟服商城启动中...")
    print(f"访问地址: http://localhost:9000")
    print(f"管理后台: http://localhost:9000/admin")

    # 清理过期订单
    try:
        expired_count = shop_manager.cleanup_expired_orders()
        if expired_count > 0:
            print(f"清理了 {expired_count} 个过期订单")
    except Exception as e:
        logger.error(f"清理过期订单失败: {str(e)}")

    app.run(debug=True, host='0.0.0.0', port=9000)

