#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商城数据模型
定义商品、订单等数据结构
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

@dataclass
class Product:
    """商品模型"""
    id: str
    name: str
    amount: int  # 充值数量
    price: int   # 价格（分）
    type: str    # 类型：token/gold
    description: str = ""
    status: str = "active"  # active/inactive
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat() if self.created_at else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Product':
        """从字典创建实例"""
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)

@dataclass
class Order:
    """订单模型"""
    order_no: str
    product_id: str
    product_name: str
    product_type: str
    amount: int  # 充值数量
    price: int   # 价格（分）
    player_name: str
    status: str = "pending"  # pending/paid/completed/failed/cancelled
    payment_method: str = None
    lakala_trade_no: str = None
    pay_order_no: str = None
    created_at: datetime = None
    paid_at: datetime = None
    completed_at: datetime = None
    failed_at: datetime = None
    error_message: str = None
    extra_data: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime字段
        datetime_fields = ['created_at', 'paid_at', 'completed_at', 'failed_at']
        for field in datetime_fields:
            if data[field]:
                data[field] = data[field].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Order':
        """从字典创建实例"""
        # 处理datetime字段
        datetime_fields = ['created_at', 'paid_at', 'completed_at', 'failed_at']
        for field in datetime_fields:
            if field in data and data[field]:
                try:
                    # 尝试解析ISO格式
                    data[field] = datetime.fromisoformat(data[field])
                except (ValueError, TypeError):
                    try:
                        # 尝试解析SQLite默认格式
                        data[field] = datetime.strptime(data[field], '%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError):
                        # 如果都失败了，设为None
                        data[field] = None

        # 移除数据库中的id字段（如果存在）
        if 'id' in data:
            del data['id']

        return cls(**data)
    
    @staticmethod
    def generate_order_no() -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        random_part = str(uuid.uuid4()).replace('-', '')[:8].upper()
        return f"ORD{timestamp}{random_part}"
    
    def update_status(self, status: str, **kwargs):
        """更新订单状态"""
        valid_statuses = ['pending', 'paid', 'completed', 'failed', 'cancelled']
        if status not in valid_statuses:
            raise ValueError(f"无效的订单状态: {status}")
        
        self.status = status
        
        # 根据状态设置时间戳
        if status == 'paid':
            self.paid_at = datetime.now()
        elif status == 'completed':
            self.completed_at = datetime.now()
        elif status == 'failed':
            self.failed_at = datetime.now()
        
        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def is_expired(self, expire_hours: int = 24) -> bool:
        """检查订单是否过期"""
        if self.status != 'pending':
            return False
        
        if not self.created_at:
            return False
        
        from datetime import timedelta
        expire_time = self.created_at + timedelta(hours=expire_hours)
        return datetime.now() > expire_time
    
    def get_display_status(self) -> str:
        """获取显示用的状态文本"""
        status_map = {
            'pending': '待支付',
            'paid': '已支付',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        }
        return status_map.get(self.status, self.status)
