#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拉卡拉支付客户端
基于原有lakala_client.py重构
"""

import json
import time
import random
import string
import base64
import requests
import logging
from datetime import datetime
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography import x509
from .config import payment_config

logger = logging.getLogger(__name__)

class LakalaClient:
    """拉卡拉支付客户端"""
    
    def __init__(self, appid: str = None, is_test: bool = None):
        # 使用传入参数或配置文件中的值
        self.appid = appid or payment_config.APPID
        self.is_test = is_test if is_test is not None else payment_config.IS_TEST
        
        # 设置网关地址
        if self.is_test:
            self.gateway_url = 'https://test.wsmsd.cn/sit'
        else:
            self.gateway_url = 'https://s2.lakala.com'
        
        self.version = '3.0'
        self.schema = 'LKLAPI-SHA256withRSA'
        
        # 证书文件路径
        self.platform_cert_file = payment_config.get_cert_path(
            'platform_test' if self.is_test else 'platform_prod'
        )
        self.merchant_cert_file = payment_config.get_cert_path('merchant_cert')
        self.merchant_private_file = payment_config.get_cert_path('merchant_key')
        
        # 请求和响应体（用于调试）
        self.request_body = None
        self.response_body = None
    
    def random_string(self, length: int = 12) -> str:
        """生成随机字符串"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def execute(self, path: str, params: dict) -> dict:
        """发起API请求"""
        requrl = self.gateway_url + path
        public_params = {
            'req_time': datetime.now().strftime('%Y%m%d%H%M%S'),
            'version': self.version,
            'req_data': params
        }
        
        body = json.dumps(public_params, ensure_ascii=False, separators=(',', ':'))
        authorization = self.get_authorization(body)
        self.request_body = body
        
        logger.info(f"发送拉卡拉API请求: {requrl}")
        logger.debug(f"请求体: {body}")
        
        resp = self.curl(requrl, body, authorization)
        self.response_body = resp
        result = json.loads(resp)
        
        logger.debug(f"响应体: {resp}")
        
        if result.get('code') == 'BBS00000':
            return result['resp_data']
        elif result.get('msg'):
            error_msg = f"[{result.get('code')}]{result.get('msg')}"
            logger.error(f"拉卡拉API错误: {error_msg}")
            raise Exception(error_msg)
        else:
            logger.error("拉卡拉API返回数据解析失败")
            raise Exception('返回数据解析失败')
    
    def cashier(self, path: str, params: dict) -> dict:
        """收银台请求"""
        requrl = self.gateway_url + path
        public_params = {
            'req_time': datetime.now().strftime('%Y%m%d%H%M%S'),
            'version': '1.0',
            'req_data': params
        }
        
        body = json.dumps(public_params, ensure_ascii=False, separators=(',', ':'))
        authorization = self.get_authorization(body)
        self.request_body = body
        
        logger.info(f"发送收银台请求: {requrl}")
        logger.debug(f"请求体: {body}")
        
        resp = self.curl(requrl, body, authorization)
        self.response_body = resp
        result = json.loads(resp)
        
        logger.debug(f"响应体: {resp}")
        
        if result.get('code') == '000000':
            return result['resp_data']
        elif result.get('msg'):
            error_msg = f"[{result.get('code')}]{result.get('msg')}"
            logger.error(f"收银台API错误: {error_msg}")
            raise Exception(error_msg)
        else:
            logger.error("收银台API返回数据解析失败")
            raise Exception('返回数据解析失败')
    
    def get_authorization(self, body: str) -> str:
        """生成签名"""
        mch_serial_no = self.get_mch_serial_no()
        nonce_str = self.random_string(12)
        timestamp = int(time.time())
        
        message = f"{self.appid}\n{mch_serial_no}\n{timestamp}\n{nonce_str}\n{body}\n"
        signature = self.rsa_private_sign(message)
        
        return f'{self.schema} appid="{self.appid}",serial_no="{mch_serial_no}",timestamp="{timestamp}",nonce_str="{nonce_str}",signature="{signature}"'

    def curl(self, url: str, body: str, authorization: str) -> str:
        """发送HTTP请求"""
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': authorization,
            'User-Agent': 'LakalaPayment/1.0'
        }

        try:
            response = requests.post(
                url,
                data=body.encode('utf-8'),
                headers=headers,
                timeout=payment_config.REQUEST_TIMEOUT
            )
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP请求失败: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")

    def get_mch_serial_no(self) -> str:
        """获取商户证书序列号"""
        try:
            with open(self.merchant_cert_file, 'rb') as f:
                cert_data = f.read()

            cert = x509.load_pem_x509_certificate(cert_data)
            serial_number = cert.serial_number
            return format(serial_number, 'X')
        except Exception as e:
            logger.error(f"读取商户证书序列号失败: {str(e)}")
            raise Exception(f"证书读取失败: {str(e)}")

    def rsa_private_sign(self, message: str) -> str:
        """RSA私钥签名"""
        try:
            with open(self.merchant_private_file, 'rb') as f:
                private_key_data = f.read()

            private_key = serialization.load_pem_private_key(
                private_key_data,
                password=None
            )

            signature = private_key.sign(
                message.encode('utf-8'),
                padding.PKCS1v15(),
                hashes.SHA256()
            )

            return base64.b64encode(signature).decode('utf-8')
        except Exception as e:
            logger.error(f"RSA签名失败: {str(e)}")
            raise Exception(f"签名失败: {str(e)}")

    def verify_sign(self, authorization: str, body: str) -> bool:
        """验证签名"""
        try:
            # 解析Authorization头
            auth_parts = {}
            auth_str = authorization.replace(f'{self.schema} ', '')
            for part in auth_str.split(','):
                key, value = part.split('=', 1)
                auth_parts[key] = value.strip('"')

            # 构建验证消息
            message = f"{auth_parts['appid']}\n{auth_parts['serial_no']}\n{auth_parts['timestamp']}\n{auth_parts['nonce_str']}\n{body}\n"

            # 加载平台证书
            with open(self.platform_cert_file, 'rb') as f:
                cert_data = f.read()

            cert = x509.load_pem_x509_certificate(cert_data)
            public_key = cert.public_key()

            # 验证签名
            signature = base64.b64decode(auth_parts['signature'])
            public_key.verify(
                signature,
                message.encode('utf-8'),
                padding.PKCS1v15(),
                hashes.SHA256()
            )

            return True
        except Exception as e:
            logger.error(f"签名验证失败: {str(e)}")
            return False
