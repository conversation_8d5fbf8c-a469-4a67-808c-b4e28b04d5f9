#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付处理器
处理支付订单创建、状态查询等业务逻辑
"""

import json
import logging
import base64
import io
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
import qrcode
from PIL import Image
from .client import LakalaClient
from .config import payment_config

logger = logging.getLogger(__name__)

class PaymentProcessor:
    """支付处理器"""
    
    def __init__(self):
        self.client = LakalaClient()
        self.config = payment_config

    def generate_qrcode(self, data: str, size: int = 10) -> str:
        """
        生成二维码并返回base64编码的图片

        Args:
            data: 要编码的数据（支付链接）
            size: 二维码大小（1-40）

        Returns:
            base64编码的PNG图片字符串
        """
        try:
            # 创建二维码实例
            qr = qrcode.QRCode(
                version=1,  # 控制二维码的大小，1是21x21的矩阵
                error_correction=qrcode.constants.ERROR_CORRECT_L,  # 错误纠正级别
                box_size=size,  # 每个小格子包含的像素数
                border=4,  # 边框的格子厚度
            )

            # 添加数据
            qr.add_data(data)
            qr.make(fit=True)

            # 创建图片
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)

            # 编码为base64字符串
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            logger.info(f"二维码生成成功，数据长度: {len(data)}")
            return img_base64

        except Exception as e:
            logger.error(f"生成二维码失败: {str(e)}")
            return None
    
    def create_qrcode_payment(self, order_no: str, account_type: str, amount: int, 
                            subject: str, request_ip: str = '127.0.0.1') -> Dict[str, Any]:
        """
        创建聚合扫码支付订单
        
        Args:
            order_no: 商户订单号
            account_type: 账户类型 (ALIPAY/WECHAT/UQRCODEPAY)
            amount: 支付金额（分）
            subject: 订单标题
            request_ip: 请求IP
            
        Returns:
            支付结果
        """
        params = {
            'merchant_no': self.config.MERCHANT_NO,
            'term_no': self.config.TERM_NO,
            'out_trade_no': order_no,
            'account_type': account_type,
            'trans_type': self.config.TRANS_TYPES['qrcode'],
            'total_amount': str(amount),
            'location_info': {
                'request_ip': request_ip
            },
            'subject': subject,
            'notify_url': self.config.NOTIFY_URL
        }
        
        try:
            logger.info(f"创建扫码支付订单: {order_no}, 金额: {amount}, 类型: {account_type}")
            result = self.client.execute('/api/v3/labs/trans/preorder', params)

            # 获取支付链接
            payment_code = result.get('acc_resp_fields', {}).get('code')
            qr_code_base64 = None

            if payment_code:
                # 将支付链接转换为二维码
                qr_code_base64 = self.generate_qrcode(payment_code)
                logger.info(f"支付链接转换为二维码: {payment_code[:50]}...")
            else:
                logger.warning("拉卡拉未返回支付链接")

            return {
                'success': True,
                'payment_type': 'qrcode',
                'lakala_trade_no': result.get('trade_no'),
                'payment_code': payment_code,  # 原始支付链接
                'qr_code': qr_code_base64,     # base64编码的二维码图片
                'expire_time': self._calculate_expire_time()
            }
        except Exception as e:
            logger.error(f"创建扫码支付订单失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_cashier_payment(self, order_no: str, amount: int, subject: str, pay_mode: str = 'ALL') -> Dict[str, Any]:
        """
        创建收银台支付订单

        Args:
            order_no: 商户订单号
            amount: 支付金额（分）
            subject: 订单标题
            pay_mode: 支付方式 (ALL/ALIPAY/WECHAT/UNION)

        Returns:
            支付结果
        """
        expire_time = datetime.now() + timedelta(minutes=self.config.ORDER_EXPIRE_MINUTES)

        # 构建带订单号的回调地址
        callback_url = f"{self.config.RETURN_URL}?out_order_no={order_no}"

        params = {
            'out_order_no': order_no,
            'merchant_no': self.config.MERCHANT_NO,
            'total_amount': str(amount),
            'order_efficient_time': expire_time.strftime('%Y%m%d%H%M%S'),
            'notify_url': self.config.CASHIER_NOTIFY_URL,
            'support_refund': 1,
            'callback_url': callback_url,
            'order_info': subject,
            'counter_param': json.dumps({'pay_mode': pay_mode})
        }
        
        try:
            logger.info(f"创建收银台支付订单: {order_no}, 金额: {amount}, 回调地址: {callback_url}")
            result = self.client.cashier('/api/v3/ccss/counter/order/special_create', params)
            
            return {
                'success': True,
                'payment_type': 'cashier',
                'pay_order_no': result.get('pay_order_no'),
                'counter_url': result.get('counter_url'),
                'expire_time': expire_time.isoformat()
            }
        except Exception as e:
            logger.error(f"创建收银台支付订单失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_cashier_qrcode(self, counter_url: str) -> Dict[str, Any]:
        """
        获取收银台二维码

        Args:
            counter_url: 收银台URL

        Returns:
            包含二维码的结果
        """
        try:
            logger.info(f"开始获取收银台二维码: {counter_url}")

            # 访问收银台页面
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(counter_url, headers=headers, timeout=30)
            response.raise_for_status()

            # 解析页面内容，查找二维码URL
            content = response.text

            # 查找二维码相关的URL或数据
            import re

            # 查找可能的二维码URL
            qr_patterns = [
                r'qrCode["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'qr_code["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'data:image/png;base64,([A-Za-z0-9+/=]+)',
                r'https?://[^"\'\s]+\.(?:png|jpg|jpeg|gif)',
            ]

            qr_url = None
            for pattern in qr_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    qr_url = matches[0]
                    logger.info(f"找到二维码URL: {qr_url}")
                    break

            if qr_url:
                # 如果找到二维码URL，生成二维码图片
                if qr_url.startswith('data:image'):
                    # Base64编码的图片
                    return {
                        'success': True,
                        'qr_code': qr_url,
                        'payment_type': 'cashier_qr'
                    }
                else:
                    # 普通URL，生成二维码
                    qr_code_data = self.generate_qrcode(qr_url)
                    return {
                        'success': True,
                        'qr_code': qr_code_data,
                        'payment_type': 'cashier_qr',
                        'qr_url': qr_url
                    }
            else:
                # 如果没有找到二维码，直接用收银台URL生成二维码
                logger.warning("未找到收银台二维码，使用收银台URL生成二维码")
                qr_code_data = self.generate_qrcode(counter_url)
                return {
                    'success': True,
                    'qr_code': qr_code_data,
                    'payment_type': 'cashier_qr',
                    'qr_url': counter_url
                }

        except Exception as e:
            logger.error(f"获取收银台二维码失败: {str(e)}")
            # 失败时返回收银台URL的二维码
            try:
                qr_code_data = self.generate_qrcode(counter_url)
                return {
                    'success': True,
                    'qr_code': qr_code_data,
                    'payment_type': 'cashier_qr',
                    'qr_url': counter_url,
                    'fallback': True
                }
            except Exception as qr_error:
                logger.error(f"生成收银台URL二维码也失败: {str(qr_error)}")
                return {
                    'success': False,
                    'error': f'获取收银台二维码失败: {str(e)}'
                }
    
    def create_payment(self, order_no: str, payment_method: str, amount: int,
                      subject: str, request_ip: str = '127.0.0.1') -> Dict[str, Any]:
        """
        创建支付订单（统一入口）

        Args:
            order_no: 商户订单号
            payment_method: 支付方式 (alipay/wechat/unionpay/cashier)
            amount: 支付金额（分）
            subject: 订单标题
            request_ip: 请求IP

        Returns:
            支付结果
        """
        if payment_method == 'cashier':
            return self.create_cashier_payment(order_no, amount, subject)

        # 检查是否强制使用收银台
        payment_config = self.config.SUPPORTED_PAY_TYPES.get(payment_method, {})
        if payment_config.get('use_cashier', False):
            logger.info(f"支付方式 {payment_method} 配置为使用收银台模式")
            pay_mode = payment_config.get('pay_mode', 'ALL')

            # 创建收银台支付
            cashier_result = self.create_cashier_payment(order_no, amount, subject, pay_mode)
            if cashier_result['success']:
                # 获取收银台二维码
                qr_result = self.get_cashier_qrcode(cashier_result['counter_url'])
                if qr_result['success']:
                    # 合并结果
                    cashier_result.update({
                        'qr_code': qr_result['qr_code'],
                        'payment_type': 'cashier_qr',
                        'qr_url': qr_result.get('qr_url'),
                        'fallback': qr_result.get('fallback', False)
                    })
                    logger.info(f"收银台二维码获取成功: {payment_method}")
                else:
                    logger.warning(f"收银台二维码获取失败，保持原有跳转模式: {qr_result.get('error')}")

            return cashier_result

        # 获取账户类型映射
        account_type_map = {
            'alipay': 'ALIPAY',
            'wechat': 'WECHAT',
            'unionpay': 'UQRCODEPAY'
        }

        account_type = account_type_map.get(payment_method)
        if not account_type:
            return {
                'success': False,
                'error': f'不支持的支付方式: {payment_method}'
            }

        # 尝试创建扫码支付
        result = self.create_qrcode_payment(order_no, account_type, amount, subject, request_ip)

        # 如果扫码支付失败且是权限问题，自动降级到收银台
        if not result['success'] and ('BBS16111' in result.get('error', '') or '未开通支付业务' in result.get('error', '')):
            logger.warning(f"扫码支付权限问题，自动降级到收银台: {payment_method}")
            pay_mode = payment_config.get('pay_mode', 'ALL')
            return self.create_cashier_payment(order_no, amount, subject, pay_mode)

        return result
    
    def verify_notify(self, authorization: str, body: str) -> bool:
        """验证支付通知签名"""
        try:
            return self.client.verify_sign(authorization, body)
        except Exception as e:
            logger.error(f"验证通知签名失败: {str(e)}")
            return False
    
    def parse_notify_data(self, body: str) -> Optional[Dict[str, Any]]:
        """解析通知数据"""
        try:
            return json.loads(body)
        except Exception as e:
            logger.error(f"解析通知数据失败: {str(e)}")
            return None
    
    def _calculate_expire_time(self) -> str:
        """计算订单过期时间"""
        expire_time = datetime.now() + timedelta(minutes=self.config.ORDER_EXPIRE_MINUTES)
        return expire_time.isoformat()
    
    def get_supported_payment_methods(self) -> Dict[str, Any]:
        """获取支持的支付方式"""
        methods = dict(self.config.SUPPORTED_PAY_TYPES)
        methods['cashier'] = {
            'name': '收银台支付',
            'account_type': 'ALL',
            'pay_mode': 'ALL'
        }
        return methods

# 创建全局支付处理器实例
payment_processor = PaymentProcessor()
